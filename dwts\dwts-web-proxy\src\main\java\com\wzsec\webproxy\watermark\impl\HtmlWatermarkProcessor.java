package com.wzsec.webproxy.watermark.impl;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * HTML页面水印处理器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class HtmlWatermarkProcessor extends AbstractWatermarkProcessor {

    private static final Pattern HEAD_PATTERN = Pattern.compile("</head>", Pattern.CASE_INSENSITIVE);
    private static final Pattern BODY_PATTERN = Pattern.compile("</body>", Pattern.CASE_INSENSITIVE);

    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        try {
            if (!config.getEnablePageWatermark()) {
                return content;
            }

            String html = safeToString(content);
            if (html.trim().isEmpty()) {
                return content;
            }

            // 生成水印文本
            String watermarkText = generateWatermarkText(request, config);
            
            // 注入水印
            String watermarkedHtml = injectWatermark(html, watermarkText, request, config);
            
            return safeToBytes(watermarkedHtml);
            
        } catch (Exception e) {
            log.error("HTML水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 注入水印到HTML中
     */
    private String injectWatermark(String html, String watermarkText, 
                                 HttpServletRequest request, WebProxyConfig config) {
        
        // 1. 注入水印样式
        html = injectWatermarkStyle(html, watermarkText, config);
        
        // 2. 注入水印脚本
        html = injectWatermarkScript(html, config);
        
        // 3. 重写页面链接（如果启用）
        if (config.getEnableLinkRewrite()) {
            html = rewritePageLinks(html, request, config);
        }
        
        // 4. 注入API拦截脚本（如果启用）
        if (config.getEnableApiIntercept()) {
            html = injectApiInterceptor(html, request, config);
        }
        
        return html;
    }

    /**
     * 注入水印样式
     */
    private String injectWatermarkStyle(String html, String watermarkText, WebProxyConfig config) {
        String base64Svg = generateWatermarkSvg(watermarkText, config);
        double opacity = config.getWatermarkOpacity() != null ? config.getWatermarkOpacity() : 0.1;
        int width = config.getWatermarkWidth() != null ? config.getWatermarkWidth() : 300;
        int height = config.getWatermarkHeight() != null ? config.getWatermarkHeight() : 150;
        
        String watermarkStyle = String.format("""
            <style id="dwts-watermark-style">
            .dwts-watermark {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100%% !important;
                height: 100%% !important;
                pointer-events: none !important;
                z-index: 999999 !important;
                opacity: %.2f !important;
                background-image: url('data:image/svg+xml;base64,%s') !important;
                background-repeat: repeat !important;
                background-size: %dpx %dpx !important;
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
            }
            </style>
            """, opacity, base64Svg, width, height);
        
        return HEAD_PATTERN.matcher(html).replaceFirst(watermarkStyle + "</head>");
    }

    /**
     * 注入水印脚本
     */
    private String injectWatermarkScript(String html, WebProxyConfig config) {
        String watermarkScript = """
            <script id="dwts-watermark-script">
            (function() {
                'use strict';
                
                var watermarkId = 'dwts-watermark-div';
                var watermarkClass = 'dwts-watermark';
                var checkInterval = 1000; // 检查间隔1秒
                
                function createWatermark() {
                    // 移除已存在的水印
                    var existingWatermark = document.getElementById(watermarkId);
                    if (existingWatermark) {
                        existingWatermark.remove();
                    }
                    
                    var watermark = document.createElement('div');
                    watermark.id = watermarkId;
                    watermark.className = watermarkClass;
                    
                    // 防止被删除和修改
                    Object.defineProperty(watermark, 'remove', {
                        value: function() { 
                            setTimeout(createWatermark, 100); 
                        },
                        writable: false,
                        configurable: false
                    });
                    
                    Object.defineProperty(watermark, 'style', {
                        value: watermark.style,
                        writable: false,
                        configurable: false
                    });
                    
                    // 添加到页面
                    if (document.body) {
                        document.body.appendChild(watermark);
                    } else {
                        document.documentElement.appendChild(watermark);
                    }
                    
                    return watermark;
                }
                
                function checkWatermark() {
                    var watermark = document.getElementById(watermarkId);
                    if (!watermark || !document.contains(watermark)) {
                        createWatermark();
                    }
                }
                
                // 页面加载完成后创建水印
                function initWatermark() {
                    createWatermark();
                    
                    // 定期检查水印是否存在
                    setInterval(checkWatermark, checkInterval);
                    
                    // 监控DOM变化
                    if (window.MutationObserver) {
                        var observer = new MutationObserver(function(mutations) {
                            var needCheck = false;
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'childList') {
                                    mutation.removedNodes.forEach(function(node) {
                                        if (node.id === watermarkId) {
                                            needCheck = true;
                                        }
                                    });
                                }
                            });
                            if (needCheck) {
                                setTimeout(checkWatermark, 50);
                            }
                        });
                        
                        observer.observe(document.body || document.documentElement, {
                            childList: true,
                            subtree: true
                        });
                    }
                }
                
                // 等待DOM加载完成
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', initWatermark);
                } else {
                    initWatermark();
                }
                
                // 防止控制台删除水印
                if (window.console) {
                    var originalLog = console.log;
                    console.log = function() {
                        checkWatermark();
                        return originalLog.apply(console, arguments);
                    };
                }
                
            })();
            </script>
            """;
        
        return BODY_PATTERN.matcher(html).replaceFirst(watermarkScript + "</body>");
    }

    /**
     * 重写页面中的链接
     */
    private String rewritePageLinks(String html, HttpServletRequest request, WebProxyConfig config) {
        String proxyBaseUrl = getProxyBaseUrl(request);
        String targetBaseUrl = config.getTargetBaseUrl();
        
        // 重写绝对URL
        html = html.replaceAll("(?i)href=[\"']" + Pattern.quote(targetBaseUrl), 
                              "href=\"" + proxyBaseUrl);
        html = html.replaceAll("(?i)src=[\"']" + Pattern.quote(targetBaseUrl), 
                              "src=\"" + proxyBaseUrl);
        
        return html;
    }

    /**
     * 注入API拦截脚本
     */
    private String injectApiInterceptor(String html, HttpServletRequest request, WebProxyConfig config) {
        String proxyBaseUrl = getProxyBaseUrl(request);
        String[] apiPatterns = config.getApiPathPatterns().split(",");
        
        StringBuilder patternsJs = new StringBuilder("[");
        for (int i = 0; i < apiPatterns.length; i++) {
            if (i > 0) patternsJs.append(",");
            patternsJs.append("'").append(apiPatterns[i].trim().replace("**", "")).append("'");
        }
        patternsJs.append("]");
        
        String apiInterceptorScript = String.format("""
            <script id="dwts-api-interceptor">
            (function() {
                'use strict';
                
                var proxyBaseUrl = '%s';
                var apiPatterns = %s;
                
                function isApiUrl(url) {
                    if (typeof url !== 'string') return false;
                    return apiPatterns.some(function(pattern) {
                        return url.startsWith(pattern);
                    });
                }
                
                function rewriteUrl(url) {
                    if (isApiUrl(url)) {
                        return proxyBaseUrl + url;
                    }
                    return url;
                }
                
                // 拦截fetch请求
                if (window.fetch) {
                    var originalFetch = window.fetch;
                    window.fetch = function(url, options) {
                        if (typeof url === 'string') {
                            url = rewriteUrl(url);
                        } else if (url && url.url) {
                            url.url = rewriteUrl(url.url);
                        }
                        return originalFetch.call(this, url, options);
                    };
                }
                
                // 拦截XMLHttpRequest
                if (window.XMLHttpRequest) {
                    var originalOpen = XMLHttpRequest.prototype.open;
                    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                        url = rewriteUrl(url);
                        return originalOpen.call(this, method, url, async, user, password);
                    };
                }
                
                // 拦截axios（如果存在）
                if (window.axios && axios.interceptors) {
                    axios.interceptors.request.use(function(config) {
                        if (config.url) {
                            config.url = rewriteUrl(config.url);
                        }
                        return config;
                    });
                }
                
            })();
            </script>
            """, proxyBaseUrl, patternsJs.toString());
        
        return HEAD_PATTERN.matcher(html).replaceFirst(apiInterceptorScript + "</head>");
    }

    /**
     * 获取代理基础URL
     */
    private String getProxyBaseUrl(HttpServletRequest request) {
        StringBuilder url = new StringBuilder();
        url.append(request.getScheme()).append("://").append(request.getServerName());
        
        int port = request.getServerPort();
        if (port != 80 && port != 443) {
            url.append(":").append(port);
        }
        
        return url.toString();
    }

    @Override
    public boolean canHandle(String contentType) {
        return isContentTypeMatch(contentType, "text/html");
    }

    @Override
    public String getProcessorName() {
        return "HtmlWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "PAGE";
    }
}
