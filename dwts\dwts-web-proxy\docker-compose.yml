version: '3.8'

services:
  # DWTS Web代理服务
  dwts-web-proxy:
    build: .
    container_name: dwts-web-proxy
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=password
      - DB_USERNAME=root
      - DB_PASSWORD=password
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - dwts-network
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/dwts-web-proxy

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: dwts-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=dwts-clean
      - MYSQL_USER=dwts
      - MYSQL_PASSWORD=dwts123
      - TZ=Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - dwts-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: dwts-redis
    ports:
      - "6379:6379"
    networks:
      - dwts-network
    restart: unless-stopped
    volumes:
      - redis_data:/data

networks:
  dwts-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
