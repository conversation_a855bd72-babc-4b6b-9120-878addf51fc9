-- DWTS Web代理初始化数据

USE `dwts`;

-- 插入示例代理配置
INSERT INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES
(
    'Vue应用代理示例', 8080, '*************', 80, 'http',
    'DWTS水印_{IP}_{DATE}', 1, 1,
    '/api/**,/rest/**,/service/**', 0.1, 300, 150,
    '#666666', -30.0, 1, 1,
    'ACTIVE', 'Vue应用代理配置示例', 'system'
),
(
    'API服务代理', 8081, '*************', 8080, 'https',
    'API代理_{IP}_{TIME}', 0, 1,
    '/api/**,/v1/**,/v2/**', 0.15, 250, 120,
    '#999999', -25.0, 0, 1,
    'ACTIVE', 'API服务专用代理', 'system'
),
(
    '管理后台代理', 8082, '*************', 3000, 'http',
    '管理后台_{USER}_{DATE}', 1, 1,
    '/admin/api/**,/management/**', 0.08, 350, 180,
    '#333333', -35.0, 1, 1,
    'ACTIVE', '管理后台代理配置', 'system'
);

-- 创建存储过程：清理过期访问记录
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `sp_cleanup_old_records`(IN days_to_keep INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;

    DELETE FROM `dwts_web_proxy_record`
    WHERE `create_time` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);

    SET deleted_count = ROW_COUNT();

    SELECT CONCAT('已删除 ', deleted_count, ' 条过期记录') AS result;
END //
DELIMITER ;

-- 创建存储过程：获取代理统计信息
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `sp_get_proxy_stats`(IN config_id BIGINT, IN days INT)
BEGIN
    SELECT
        COUNT(*) as total_requests,
        COUNT(DISTINCT request_ip) as unique_visitors,
        COUNT(CASE WHEN watermark_added = 1 THEN 1 END) as watermark_requests,
        COUNT(CASE WHEN request_type = 'PAGE' THEN 1 END) as page_requests,
        COUNT(CASE WHEN request_type = 'API' THEN 1 END) as api_requests,
        COUNT(CASE WHEN request_type = 'RESOURCE' THEN 1 END) as resource_requests,
        AVG(process_time) as avg_process_time,
        MAX(process_time) as max_process_time,
        SUM(response_size) as total_response_size,
        COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_requests
    FROM `dwts_web_proxy_record`
    WHERE proxy_config_id = config_id
    AND create_time >= DATE_SUB(NOW(), INTERVAL days DAY);
END //
DELIMITER ;
