-- DWTS Web代理初始化数据

USE `dwts`;

-- 插入示例代理配置
INSERT INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES 
(
    'Vue应用代理示例', 8080, '*************', 80, 'http',
    'DWTS水印_{IP}_{DATE}', 1, 1,
    '/api/**,/rest/**,/service/**', 0.1, 300, 150,
    '#666666', -30.0, 1, 1,
    'ACTIVE', 'Vue应用代理配置示例', 'system'
),
(
    'API服务代理', 8081, '*************', 8080, 'https',
    'API代理_{IP}_{TIME}', 0, 1,
    '/api/**,/v1/**,/v2/**', 0.15, 250, 120,
    '#999999', -25.0, 0, 1,
    'ACTIVE', 'API服务专用代理', 'system'
),
(
    '管理后台代理', 8082, '*************', 3000, 'http',
    '管理后台_{USER}_{DATE}', 1, 1,
    '/admin/api/**,/management/**', 0.08, 350, 180,
    '#333333', -35.0, 1, 1,
    'ACTIVE', '管理后台代理配置', 'system'
),
(
    '测试环境代理', 8083, 'test.example.com', 80, 'http',
    '测试环境_{IP}_{TIME}', 1, 1,
    '/api/**,/test/**', 0.2, 280, 140,
    '#FF6B6B', -20.0, 1, 1,
    'INACTIVE', '测试环境代理（已禁用）', 'system'
);

-- 插入示例访问记录（用于测试和演示）
INSERT INTO `dwts_web_proxy_record` (
    `proxy_config_id`, `request_ip`, `request_port`, `request_path`, `request_method`,
    `request_type`, `response_status`, `response_content_type`, `response_size`,
    `watermark_added`, `watermark_type`, `watermark_content`, `process_time`,
    `user_agent`, `referer`, `session_id`, `create_time`
) VALUES 
(
    1, '*************', 54321, '/', 'GET',
    'PAGE', 200, 'text/html', 15680,
    1, 'PAGE', 'DWTS水印_*************_20241219', 156,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'sess_001',
    DATE_SUB(NOW(), INTERVAL 1 HOUR)
),
(
    1, '*************', 54322, '/api/users', 'GET',
    'API', 200, 'application/json', 2340,
    1, 'JSON', 'DWTS水印_*************_20241219', 89,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'http://localhost:8080/', 'sess_001',
    DATE_SUB(NOW(), INTERVAL 50 MINUTE)
),
(
    1, '*************', 45678, '/static/css/app.css', 'GET',
    'RESOURCE', 200, 'text/css', 45120,
    0, NULL, NULL, 23,
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'http://localhost:8080/', 'sess_002',
    DATE_SUB(NOW(), INTERVAL 45 MINUTE)
),
(
    2, '*************', 56789, '/api/v1/products', 'POST',
    'API', 201, 'application/json', 1890,
    1, 'JSON', 'API代理_*************_20241219_143022', 234,
    'PostmanRuntime/7.32.3', NULL, NULL,
    DATE_SUB(NOW(), INTERVAL 30 MINUTE)
),
(
    3, '*************', 34567, '/admin', 'GET',
    'PAGE', 200, 'text/html', 28900,
    1, 'PAGE', '管理后台_admin_20241219', 198,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'sess_003',
    DATE_SUB(NOW(), INTERVAL 20 MINUTE)
),
(
    1, '*************', 54323, '/api/orders', 'GET',
    'API', 500, 'application/json', 156,
    0, NULL, NULL, 5000,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'http://localhost:8080/', 'sess_001',
    DATE_SUB(NOW(), INTERVAL 10 MINUTE)
);

-- 创建存储过程：清理过期访问记录
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `sp_cleanup_old_records`(IN days_to_keep INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    DELETE FROM `dwts_web_proxy_record` 
    WHERE `create_time` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    SET deleted_count = ROW_COUNT();
    
    SELECT CONCAT('已删除 ', deleted_count, ' 条过期记录') AS result;
END //
DELIMITER ;

-- 创建存储过程：获取代理统计信息
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `sp_get_proxy_stats`(IN config_id BIGINT, IN days INT)
BEGIN
    SELECT 
        COUNT(*) as total_requests,
        COUNT(DISTINCT request_ip) as unique_visitors,
        COUNT(CASE WHEN watermark_added = 1 THEN 1 END) as watermark_requests,
        COUNT(CASE WHEN request_type = 'PAGE' THEN 1 END) as page_requests,
        COUNT(CASE WHEN request_type = 'API' THEN 1 END) as api_requests,
        COUNT(CASE WHEN request_type = 'RESOURCE' THEN 1 END) as resource_requests,
        AVG(process_time) as avg_process_time,
        MAX(process_time) as max_process_time,
        SUM(response_size) as total_response_size,
        COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_requests
    FROM `dwts_web_proxy_record`
    WHERE proxy_config_id = config_id
    AND create_time >= DATE_SUB(NOW(), INTERVAL days DAY);
END //
DELIMITER ;

-- 创建事件：自动清理30天前的访问记录（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `evt_cleanup_old_records`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL sp_cleanup_old_records(30);

-- 插入系统配置说明
INSERT INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES 
(
    '配置说明', 9999, 'example.com', 80, 'http',
    '水印变量说明: {IP}=客户端IP, {TIME}=时间戳, {DATE}=日期, {USER}=用户', 0, 0,
    '支持Ant路径模式: /api/**, /**/service/**, /v*/**, 等', 0.1, 300, 150,
    '支持颜色: #RGB, #RRGGBB, red, blue, green 等', -30.0, 1, 1,
    'INACTIVE', '这是一个配置说明示例，请勿启用。水印透明度范围0.0-1.0，角度支持正负值。', 'system'
) ON DUPLICATE KEY UPDATE remark = VALUES(remark);
