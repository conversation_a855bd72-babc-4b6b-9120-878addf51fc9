-- =====================================================
-- DWTS Web代理服务 - 完整数据库初始化脚本
-- 包含：建表语句、索引创建、初始化数据、存储过程
-- 版本：1.0
-- 日期：2024-12-19
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `dwts` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `dwts`;

-- =====================================================
-- 1. 删除已存在的表（重新创建）
-- =====================================================
DROP TABLE IF EXISTS `dwts_web_proxy_record`;
DROP TABLE IF EXISTS `dwts_web_proxy_config`;

-- =====================================================
-- 2. 创建Web代理配置表
-- =====================================================
CREATE TABLE `dwts_web_proxy_config` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `proxy_name` VARCHAR(100) NOT NULL COMMENT '代理名称',
    `proxy_port` INT(11) NOT NULL COMMENT '代理端口',
    `target_host` VARCHAR(200) NOT NULL COMMENT '目标主机地址',
    `target_port` INT(11) NOT NULL COMMENT '目标端口',
    `target_protocol` VARCHAR(10) DEFAULT 'http' COMMENT '目标协议 (http/https)',
    `watermark_text` VARCHAR(500) DEFAULT NULL COMMENT '水印文本',
    `enable_page_watermark` TINYINT(1) DEFAULT 1 COMMENT '是否启用页面水印',
    `enable_api_watermark` TINYINT(1) DEFAULT 1 COMMENT '是否启用API水印',
    `api_path_patterns` VARCHAR(1000) DEFAULT '/api/**,/rest/**' COMMENT 'API路径模式 (逗号分隔)',
    `watermark_opacity` DOUBLE DEFAULT 0.1 COMMENT '水印透明度 (0.0-1.0)',
    `watermark_width` INT(11) DEFAULT 300 COMMENT '水印宽度',
    `watermark_height` INT(11) DEFAULT 150 COMMENT '水印高度',
    `watermark_color` VARCHAR(20) DEFAULT '#666666' COMMENT '水印颜色',
    `watermark_angle` DOUBLE DEFAULT -30.0 COMMENT '水印角度',
    `enable_link_rewrite` TINYINT(1) DEFAULT 1 COMMENT '是否启用链接重写',
    `enable_api_intercept` TINYINT(1) DEFAULT 1 COMMENT '是否启用API拦截',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态 (ACTIVE/INACTIVE)',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建用户',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新用户',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_proxy_port` (`proxy_port`),
    UNIQUE KEY `uk_proxy_name` (`proxy_name`),
    KEY `idx_status` (`status`),
    KEY `idx_target_host_port` (`target_host`, `target_port`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Web代理配置表';

-- =====================================================
-- 3. 创建Web代理访问记录表
-- =====================================================
CREATE TABLE `dwts_web_proxy_record` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `proxy_config_id` BIGINT(20) DEFAULT NULL COMMENT '代理配置ID',
    `request_ip` VARCHAR(50) DEFAULT NULL COMMENT '请求IP',
    `request_port` INT(11) DEFAULT NULL COMMENT '请求端口',
    `request_path` VARCHAR(1000) DEFAULT NULL COMMENT '请求路径',
    `request_method` VARCHAR(10) DEFAULT NULL COMMENT '请求方法',
    `request_type` VARCHAR(20) DEFAULT NULL COMMENT '请求类型 (PAGE/API/RESOURCE)',
    `response_status` INT(11) DEFAULT NULL COMMENT '响应状态码',
    `response_content_type` VARCHAR(100) DEFAULT NULL COMMENT '响应内容类型',
    `response_size` BIGINT(20) DEFAULT NULL COMMENT '响应大小（字节）',
    `watermark_added` TINYINT(1) DEFAULT 0 COMMENT '是否添加了水印',
    `watermark_type` VARCHAR(20) DEFAULT NULL COMMENT '水印类型 (PAGE/JSON/XML/HEADER)',
    `watermark_content` VARCHAR(1000) DEFAULT NULL COMMENT '水印内容',
    `process_time` BIGINT(20) DEFAULT NULL COMMENT '处理耗时（毫秒）',
    `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    `referer` VARCHAR(1000) DEFAULT NULL COMMENT '引用页面',
    `session_id` VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    `error_message` VARCHAR(1000) DEFAULT NULL COMMENT '错误信息',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_proxy_config_id` (`proxy_config_id`),
    KEY `idx_request_ip` (`request_ip`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_request_type` (`request_type`),
    KEY `idx_watermark_type` (`watermark_type`),
    KEY `idx_record_time_range` (`create_time`, `proxy_config_id`),
    KEY `idx_record_ip_time` (`request_ip`, `create_time`),
    KEY `idx_record_watermark` (`watermark_added`, `watermark_type`),
    CONSTRAINT `fk_proxy_record_config` FOREIGN KEY (`proxy_config_id`) REFERENCES `dwts_web_proxy_config` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Web代理访问记录表';

-- =====================================================
-- 4. 插入示例代理配置数据
-- =====================================================
INSERT INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES 
(
    'Vue应用代理示例', 8080, '*************', 80, 'http',
    'DWTS水印_{IP}_{DATE}', 1, 1,
    '/api/**,/rest/**,/service/**', 0.1, 300, 150,
    '#666666', -30.0, 1, 1,
    'ACTIVE', 'Vue应用代理配置示例，可以代理Vue+Nginx应用', 'system'
),
(
    'API服务代理', 8081, '*************', 8080, 'https',
    'API代理_{IP}_{TIME}', 0, 1,
    '/api/**,/v1/**,/v2/**', 0.15, 250, 120,
    '#999999', -25.0, 0, 1,
    'ACTIVE', 'API服务专用代理，只对接口添加水印', 'system'
),
(
    '管理后台代理', 8082, '*************', 3000, 'http',
    '管理后台_{USER}_{DATE}', 1, 1,
    '/admin/api/**,/management/**', 0.08, 350, 180,
    '#333333', -35.0, 1, 1,
    'ACTIVE', '管理后台代理配置，支持用户标识水印', 'system'
),
(
    '测试环境代理', 8083, 'test.example.com', 80, 'http',
    '测试环境_{IP}_{TIME}', 1, 1,
    '/api/**,/test/**', 0.2, 280, 140,
    '#FF6B6B', -20.0, 1, 1,
    'INACTIVE', '测试环境代理（已禁用）', 'system'
);

-- =====================================================
-- 5. 插入示例访问记录数据
-- =====================================================
INSERT INTO `dwts_web_proxy_record` (
    `proxy_config_id`, `request_ip`, `request_port`, `request_path`, `request_method`,
    `request_type`, `response_status`, `response_content_type`, `response_size`,
    `watermark_added`, `watermark_type`, `watermark_content`, `process_time`,
    `user_agent`, `referer`, `session_id`, `create_time`
) VALUES 
(
    1, '*************', 54321, '/', 'GET',
    'PAGE', 200, 'text/html', 15680,
    1, 'PAGE', 'DWTS水印_*************_20241219', 156,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'sess_001',
    DATE_SUB(NOW(), INTERVAL 1 HOUR)
),
(
    1, '*************', 54322, '/api/users', 'GET',
    'API', 200, 'application/json', 2340,
    1, 'JSON', 'DWTS水印_*************_20241219', 89,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'http://localhost:8080/', 'sess_001',
    DATE_SUB(NOW(), INTERVAL 50 MINUTE)
),
(
    1, '*************', 45678, '/static/css/app.css', 'GET',
    'RESOURCE', 200, 'text/css', 45120,
    0, NULL, NULL, 23,
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'http://localhost:8080/', 'sess_002',
    DATE_SUB(NOW(), INTERVAL 45 MINUTE)
),
(
    2, '*************', 56789, '/api/v1/products', 'POST',
    'API', 201, 'application/json', 1890,
    1, 'JSON', 'API代理_*************_20241219_143022', 234,
    'PostmanRuntime/7.32.3', NULL, NULL,
    DATE_SUB(NOW(), INTERVAL 30 MINUTE)
),
(
    3, '*************', 34567, '/admin', 'GET',
    'PAGE', 200, 'text/html', 28900,
    1, 'PAGE', '管理后台_admin_20241219', 198,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, 'sess_003',
    DATE_SUB(NOW(), INTERVAL 20 MINUTE)
);

-- =====================================================
-- 6. 创建存储过程
-- =====================================================

-- 删除已存在的存储过程
DROP PROCEDURE IF EXISTS `sp_cleanup_old_records`;
DROP PROCEDURE IF EXISTS `sp_get_proxy_stats`;

-- 创建存储过程：清理过期访问记录
DELIMITER //
CREATE PROCEDURE `sp_cleanup_old_records`(IN days_to_keep INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    DELETE FROM `dwts_web_proxy_record` 
    WHERE `create_time` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    SET deleted_count = ROW_COUNT();
    
    SELECT CONCAT('已删除 ', deleted_count, ' 条过期记录') AS result;
END //
DELIMITER ;

-- 创建存储过程：获取代理统计信息
DELIMITER //
CREATE PROCEDURE `sp_get_proxy_stats`(IN config_id BIGINT, IN days INT)
BEGIN
    SELECT 
        COUNT(*) as total_requests,
        COUNT(DISTINCT request_ip) as unique_visitors,
        COUNT(CASE WHEN watermark_added = 1 THEN 1 END) as watermark_requests,
        COUNT(CASE WHEN request_type = 'PAGE' THEN 1 END) as page_requests,
        COUNT(CASE WHEN request_type = 'API' THEN 1 END) as api_requests,
        COUNT(CASE WHEN request_type = 'RESOURCE' THEN 1 END) as resource_requests,
        AVG(process_time) as avg_process_time,
        MAX(process_time) as max_process_time,
        SUM(response_size) as total_response_size,
        COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_requests
    FROM `dwts_web_proxy_record`
    WHERE proxy_config_id = config_id
    AND create_time >= DATE_SUB(NOW(), INTERVAL days DAY);
END //
DELIMITER ;

-- =====================================================
-- 7. 创建视图
-- =====================================================

-- 删除已存在的视图
DROP VIEW IF EXISTS `v_proxy_config_stats`;
DROP VIEW IF EXISTS `v_access_stats`;

-- 创建视图：代理配置统计
CREATE VIEW `v_proxy_config_stats` AS
SELECT 
    c.id,
    c.proxy_name,
    c.proxy_port,
    c.target_host,
    c.target_port,
    c.status,
    COUNT(r.id) as total_requests,
    COUNT(CASE WHEN r.watermark_added = 1 THEN 1 END) as watermark_requests,
    AVG(r.process_time) as avg_process_time,
    MAX(r.create_time) as last_access_time
FROM `dwts_web_proxy_config` c
LEFT JOIN `dwts_web_proxy_record` r ON c.id = r.proxy_config_id
GROUP BY c.id, c.proxy_name, c.proxy_port, c.target_host, c.target_port, c.status;

-- 创建视图：访问统计
CREATE VIEW `v_access_stats` AS
SELECT 
    DATE(create_time) as access_date,
    request_type,
    watermark_type,
    COUNT(*) as request_count,
    COUNT(DISTINCT request_ip) as unique_ips,
    AVG(process_time) as avg_process_time,
    SUM(response_size) as total_response_size
FROM `dwts_web_proxy_record`
WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(create_time), request_type, watermark_type
ORDER BY access_date DESC, request_count DESC;

-- =====================================================
-- 8. 插入配置说明数据
-- =====================================================
INSERT INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES 
(
    '配置说明', 9999, 'example.com', 80, 'http',
    '水印变量: {IP}=客户端IP, {TIME}=时间戳, {DATE}=日期, {USER}=用户', 0, 0,
    'Ant路径模式: /api/**, /**/service/**, /v*/**, 等', 0.1, 300, 150,
    '颜色格式: #RGB, #RRGGBB, red, blue, green 等', -30.0, 1, 1,
    'INACTIVE', '配置说明示例，请勿启用。透明度0.0-1.0，角度支持正负值。', 'system'
);

-- =====================================================
-- 9. 显示初始化结果
-- =====================================================
SELECT '数据库初始化完成！' as message;
SELECT COUNT(*) as config_count FROM dwts_web_proxy_config WHERE status = 'ACTIVE';
SELECT COUNT(*) as record_count FROM dwts_web_proxy_record;

-- 显示配置信息
SELECT 
    proxy_name as '代理名称',
    proxy_port as '代理端口', 
    CONCAT(target_host, ':', target_port) as '目标地址',
    status as '状态'
FROM dwts_web_proxy_config 
ORDER BY proxy_port;
