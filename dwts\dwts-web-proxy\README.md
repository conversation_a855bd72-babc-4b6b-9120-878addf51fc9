# DWTS Web代理服务

DWTS Web代理服务是一个基于Spring Boot的智能代理服务，支持对Vue应用和API接口进行水印加注，实现数据溯源和安全防护。

## 功能特性

### 🌟 核心功能
- **智能代理转发**: 支持HTTP/HTTPS协议的透明代理转发
- **页面水印**: 自动为HTML页面添加防篡改水印
- **API水印**: 为JSON/XML接口响应添加水印信息
- **链接重写**: 自动重写页面中的链接，确保通过代理访问
- **API拦截**: 前端API调用自动通过代理服务器

### 🛡️ 安全特性
- **水印防护**: 防止水印被删除或篡改
- **访问记录**: 完整记录所有代理访问日志
- **IP溯源**: 支持客户端IP地址追踪
- **用户标识**: 支持用户身份水印标记

### ⚡ 性能特性
- **连接池**: 使用Apache HttpClient连接池
- **配置缓存**: 代理配置内存缓存，提升性能
- **异步日志**: 访问记录异步保存，不影响响应速度
- **智能检测**: 自动检测内容类型，选择合适的处理策略

## 快速开始

### 环境要求
- JDK 11+
- MySQL 8.0+
- Maven 3.6+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd dwts/dwts-web-proxy
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p < src/main/resources/sql/schema.sql

# 初始化数据
mysql -u root -p < src/main/resources/sql/data.sql
```

3. **修改配置**
```yaml
# application.yml
spring:
  datasource:
    url: ********************************
    username: your_username
    password: your_password
```

4. **启动服务**
```bash
mvn spring-boot:run
```

5. **验证安装**
访问 http://localhost:8080/actuator/health 检查服务状态

## 配置说明

### 代理配置
在数据库中配置代理规则：

```sql
INSERT INTO dwts_web_proxy_config (
    proxy_name, proxy_port, target_host, target_port, 
    watermark_text, enable_page_watermark, enable_api_watermark
) VALUES (
    'Vue应用代理', 8080, '*************', 80,
    'DWTS水印_{IP}_{DATE}', 1, 1
);
```

### 水印变量
支持以下水印变量：
- `{IP}`: 客户端IP地址
- `{TIME}`: 时间戳 (yyyyMMdd_HHmmss)
- `{DATE}`: 日期 (yyyyMMdd)
- `{USER}`: 用户标识

### API路径模式
支持Ant路径模式：
- `/api/**`: 匹配所有/api/开头的路径
- `/**/service/**`: 匹配任意层级下的service路径
- `/v*/api/**`: 匹配/v1/api/、/v2/api/等路径

## 使用示例

### 代理Vue应用
```bash
# 原始访问
http://*************:80/

# 代理访问（带水印）
http://localhost:8080/
```

### 代理API接口
```bash
# 原始API
curl http://*************:80/api/users

# 代理API（带水印）
curl http://localhost:8080/api/users
```

### 响应示例

**HTML页面水印**：
页面会自动添加半透明重复水印覆盖层

**JSON API水印**：
```json
{
  "data": [...],
  "_watermark": {
    "text": "DWTS水印_*************_20241219",
    "timestamp": 1703001234567,
    "source": "DWTS-WEB-PROXY",
    "ip": "*************",
    "user": "admin"
  }
}
```

## 监控和管理

### 健康检查
```bash
curl http://localhost:8080/actuator/health
```

### 配置刷新
```bash
# 刷新代理配置缓存
POST http://localhost:8080/actuator/refresh
```

### 访问统计
查询代理访问统计：
```sql
CALL sp_get_proxy_stats(1, 7); -- 查询配置ID=1最近7天的统计
```

## 部署指南

### Docker部署
```dockerfile
FROM openjdk:11-jre-slim
COPY target/dwts-web-proxy.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置
```yaml
# application-prod.yml
server:
  port: 8080
  tomcat:
    max-threads: 500

spring:
  datasource:
    hikari:
      maximum-pool-size: 50

logging:
  level:
    com.wzsec: INFO
  file:
    name: /var/log/dwts-web-proxy/application.log
```

## 故障排除

### 常见问题

1. **代理无响应**
   - 检查目标服务器是否可达
   - 验证代理配置是否正确
   - 查看日志中的错误信息

2. **水印不显示**
   - 确认水印功能已启用
   - 检查内容类型是否支持
   - 验证水印配置参数

3. **性能问题**
   - 调整连接池大小
   - 增加JVM内存
   - 优化数据库查询

### 日志分析
```bash
# 查看错误日志
tail -f logs/dwts-web-proxy.log | grep ERROR

# 查看代理请求日志
tail -f logs/dwts-web-proxy.log | grep "代理请求"
```

## 开发指南

### 项目结构
```
dwts-web-proxy/
├── src/main/java/com/wzsec/webproxy/
│   ├── controller/          # 控制器
│   ├── service/            # 服务层
│   ├── domain/             # 实体类
│   ├── repository/         # 数据访问层
│   ├── watermark/          # 水印处理器
│   ├── config/             # 配置类
│   └── util/               # 工具类
├── src/main/resources/
│   ├── sql/                # 数据库脚本
│   └── application*.yml    # 配置文件
└── README.md
```

### 扩展开发

**添加新的水印处理器**：
```java
@Component
public class CustomWatermarkProcessor extends AbstractWatermarkProcessor {
    @Override
    public boolean canHandle(String contentType) {
        return contentType.contains("custom/type");
    }
    
    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        // 自定义水印处理逻辑
        return content;
    }
}
```

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>
