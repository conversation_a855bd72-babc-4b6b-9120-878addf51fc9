server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    max-connections: 8192

spring:
  application:
    name: dwts-web-proxy
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************
    username: root
    password: password
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    open-in-view: false

  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

# 代理配置
web-proxy:
  # RestTemplate配置
  rest-template:
    connect-timeout: 10000
    read-timeout: 60000
    max-connections: 500
    max-connections-per-route: 50
  
  # 水印配置
  watermark:
    default-text: "DWTS水印"
    default-opacity: 0.1
    default-width: 300
    default-height: 150
    default-color: "#666666"
    default-angle: -30
  
  # 内容处理配置
  content:
    enable-link-rewrite: true
    enable-api-intercept: true
    max-content-size: 10485760  # 10MB
    
  # API路径模式
  api-patterns:
    - "/api/**"
    - "/rest/**"
    - "/service/**"
    - "/**/api/**"

# 日志配置
logging:
  level:
    com.wzsec: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/dwts-web-proxy.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 自定义配置
dwts:
  web-proxy:
    # 是否启用代理功能
    enabled: true
    # 是否启用水印功能
    watermark-enabled: true
    # 配置缓存时间（秒）
    config-cache-ttl: 300
    # 是否启用请求日志
    enable-request-logging: true
    # 是否启用响应日志
    enable-response-logging: false
